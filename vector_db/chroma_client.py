# import chromadb
# from chromadb.utils import embedding_functions

# client = chromadb.Client()
# collection_name = "call_center_docs"

# # Sử dụng OpenAI embedding (cần API key)
# openai_ef = embedding_functions.OpenAIEmbeddingFunction(
#     api_key="YOUR_OPENAI_API_KEY",
#     model_name="text-embedding-ada-002"
# )

# collection = client.get_or_create_collection(name=collection_name, embedding_function=openai_ef)

# def add_documents(documents):
#     ids = [str(i) for i in range(len(documents))]
#     texts = [doc['content'] for doc in documents]
#     metadatas = [{"source": doc['filename']} for doc in documents]
#     collection.add(ids=ids, documents=texts, metadatas=metadatas)

# def query_documents(prompt, n_results=3):
#     results = collection.query(
#         query_texts=[prompt],
#         n_results=n_results
#     )
#     return results


import chromadb
# from chromadb.utils import embedding_functions # Bỏ OpenAI EF
import google.generativeai as genai
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# --- Cấu hình Google Generative AI ---
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY") # Nên dùng biến môi trường
if not GOOGLE_API_KEY:
    raise ValueError("Bạn cần thiết lập biến môi trường GOOGLE_API_KEY")

genai.configure(api_key=GOOGLE_API_KEY)
# Chọn model embedding, ví dụ: models/embedding-001
EMBEDDING_MODEL_NAME = "models/embedding-001"

# --- ChromaDB Client ---
client = chromadb.Client()
collection_name = "call_center_docs"

# --- Hàm tạo Embedding bằng Google Generative AI ---
def generate_embedding(text: str) -> list:
    """Tạo embedding cho một đoạn văn bản bằng Google Generative AI."""
    result = genai.embed_content(
        model=EMBEDDING_MODEL_NAME,
        content=text,
        task_type="retrieval_document" # hoặc "semantic_similarity"
    )
    return result['embedding']

# --- Hàm tạo Embedding Function tùy chỉnh cho Chroma ---
class GoogleEmbeddingFunction:
    def __init__(self, model_name: str):
        self.model_name = model_name
        genai.configure(api_key=GOOGLE_API_KEY)

    def __call__(self, input):
        # input là danh sách các đoạn văn bản
        embeddings = []
        for text in input:
             # Gọi API cho từng đoạn (Chroma thường truyền list)
             embedding = generate_embedding(text)
             embeddings.append(embedding)
        return embeddings

# --- Tạo hoặc lấy Collection với Embedding Function của Google ---
google_ef = GoogleEmbeddingFunction(model_name=EMBEDDING_MODEL_NAME)
collection = client.get_or_create_collection(name=collection_name, embedding_function=google_ef)

def add_documents(documents):
    ids = [str(i) for i in range(len(documents))]
    texts = [doc['content'] for doc in documents]
    metadatas = [{"source": doc['filename']} for doc in documents]
    # Chroma sẽ tự động gọi google_ef để tạo embeddings cho texts
    collection.add(ids=ids, documents=texts, metadatas=metadatas)

def query_documents(prompt, n_results=3):
    # Tạo embedding cho truy vấn người dùng
    query_embedding = generate_embedding(prompt)
    
    results = collection.query(
        query_embeddings=[query_embedding], # Dùng query_embeddings thay vì query_texts
        n_results=n_results
    )
    return results
