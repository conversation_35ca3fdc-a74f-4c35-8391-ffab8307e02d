# AI Call Center Assistant (PoC)

**<PERSON><PERSON> tả:** Dự án này là một Proof of Concept (PoC) cho một trợ lý AI chatbot được thiết kế để hỗ trợ trung tâm gọi điện. Chatbot có thể hiểu và trả lời các câu hỏi của người dùng bằng ngôn ngữ tự nhiên, bằng cách truy xuất thông tin từ nhiều nguồn dữ liệu khác nhau như cơ sở dữ liệu nội bộ (PostgreSQL), tài liệu văn phòng (PDF, PPTX, XLSX) và bản ghi âm cuộc gọi (MP3). Hệ thống cũng cung cấp hướng dẫn đơn giản về quy trình bằng cách gợi ý phòng ban liên quan đến truy vấn của người dùng.

---

## C<PERSON>ch Thức Hoạt Động

1.  **Người Dùng Tương Tác:** Ng<PERSON>ời dùng nhập câu hỏi thông qua một giao diện web đơn giản (Streamlit).
2.  **Xử Lý Truy Vấn:** Ứng dụng backend (FastAPI) nhận câu hỏi.
3.  **Truy Xuất Thông Tin (RAG - Retrieval Augmented Generation):**
    *   Hệ thống sử dụng mô hình embedding của Google Gemini để tạo vector cho câu hỏi người dùng.
    *   Vector này được dùng để truy vấn cơ sở dữ liệu vector (ChromaDB) nhằm tìm các đoạn văn bản (chunks) liên quan nhất từ dữ liệu đã được xử lý trước đó.
4.  **Tổng Hợp Câu Trả Lời:**
    *   Các đoạn văn bản được truy xuất được gửi tới mô hình ngôn ngữ lớn Google Gemini (`gemini-1.5-flash` hoặc `gemini-1.5-pro`).
    *   Mô hình được cung cấp prompt bao gồm các đoạn thông tin và câu hỏi gốc để tạo ra câu trả lời tự nhiên và chính xác.
5.  **Hướng Dẫn Quy Trình:** Dựa trên nội dung truy vấn (ví dụ: chứa từ khóa "lỗi"), hệ thống gợi ý một phòng ban phù hợp.
6.  **Trả Lời Người Dùng:** Câu trả lời, phòng ban được gợi ý và nguồn thông tin được hiển thị lại trên giao diện người dùng.

---

## Lý Do Chọn Các Công Nghệ/Framework

| Thành Phần | Công Nghệ | Lý Do Chọn |
| :--- | :--- | :--- |
| **Backend API** | **FastAPI** | Hiệu suất cao, hỗ trợ bất đồng bộ (async), dễ phát triển API RESTful, tích hợp tốt với Python. |
| **LLM & Embedding** | **Google Generative AI (Gemini)** | Sử dụng `google-generativeai` SDK. Chọn Gemini vì yêu cầu thay thế OpenAI. Gemini cung cấp mô hình ngôn ngữ mạnh mẽ và API embedding tiện lợi. |
| **Vector Database** | **ChromaDB** | Nhẹ, dễ cài đặt và sử dụng cho PoC. Hỗ trợ tích hợp mô hình embedding tùy chỉnh. |
| **Xử Lý File** | **PyPDF2, python-docx, openpyxl** | Các thư viện Python phổ biến, ổn định và hiệu quả để trích xuất văn bản từ PDF, Word và Excel. |
| **Xử Lý Âm Thanh** | **Whisper (OpenAI)** | Mô hình chuyển đổi giọng nói sang văn bản chính xác cao, hỗ trợ nhiều ngôn ngữ. (Lưu ý: Đây là thư viện của OpenAI, không phải API trả phí). |
| **Frontend (PoC)** | **Streamlit** | Rất nhanh để xây dựng giao diện web đơn giản cho demo mà không cần frontend phức tạp. |
| **Quản Lý Môi Trường** | **Python, pip, virtualenv** | Tiêu chuẩn trong phát triển Python, giúp quản lý phụ thuộc dễ dàng. |

---

## Cách Làm (Workflow)

1.  **Thu Thập Dữ Liệu:** Đặt các file mẫu (PDF, DOCX, PPTX, XLSX, MP3) vào thư mục `data/documents`.
2.  **Xử Lý Dữ Liệu:**
    *   Chạy các script trong `processing/` để trích xuất văn bản từ file.
    *   Với file âm thanh (MP3), sử dụng `processing/audio_transcriber.py` (cần cài đặt Whisper).
3.  **Tạo Vector Database:**
    *   Chạy script (có thể tích hợp vào bước khởi tạo ứng dụng) để đọc các đoạn văn bản đã xử lý.
    *   Sử dụng mô hình embedding của Google để chuyển đổi từng đoạn văn bản thành vector.
    *   Lưu các vector cùng metadata (nguồn gốc) vào ChromaDB.
4.  **Khởi Chạy Ứng Dụng:**
    *   Chạy backend FastAPI.
    *   (Tùy chọn) Chạy frontend Streamlit.
5.  **Sử Dụng:**
    *   Người dùng nhập câu hỏi trên giao diện.
    *   Hệ thống xử lý, truy xuất thông tin, hỏi Gemini và hiển thị kết quả.

---

## Cách Chạy Code

### Yêu Cầu

*   Python 3.8 trở lên.
*   Tài khoản Google AI Studio với API Key (cho Gemini và Embedding).
*   (Tùy chọn) Tài khoản OpenAI API Key (chỉ nếu bạn muốn dùng Whisper thay vì cài đặt cục bộ).

### Các Bước

1.  **Clone hoặc Tải Xuống Dự Án:**
    ```bash
    git clone <your-repo-url-if-any>
    cd ai_call_center_bot
    ```

2.  **Tạo Môi Trường Ảo (Khuyến Khích):**
    ```bash
    python -m venv venv
    # Kích hoạt môi trường ảo
    # Trên Windows:
    venv\Scripts\activate
    # Trên macOS/Linux:
    source venv/bin/activate
    ```

3.  **Cài Đặt Các Thư Viện Cần Thiết:**
    ```bash
    pip install -r requirements.txt
    ```

4.  **Thiết Lập Biến Môi Trường:**
    Tạo file `.env` trong thư mục gốc của dự án và thêm API Key của bạn:
    ```env
    GOOGLE_API_KEY=YOUR_GOOGLE_AI_STUDIO_API_KEY_HERE
    # OPENAI_API_KEY=YOUR_OPENAI_API_KEY_HERE # Chỉ cần nếu dùng Whisper qua API
    ```
    *(Lưu ý: Đảm bảo `.env` được thêm vào `.gitignore` nếu bạn dùng Git.)*

5.  **Chuẩn Bị Dữ Liệu:**
    *   Đặt các file mẫu của bạn (PDF, DOCX, PPTX, XLSX, MP3) vào thư mục `data/documents`.
    *   *(Hiện tại, bạn cần tự chạy script xử lý dữ liệu hoặc tích hợp nó vào bước khởi tạo ứng dụng. Sẽ được cập nhật trong phiên bản tiếp theo).*

6.  **Khởi Chạy Backend (FastAPI):**
    ```bash
    uvicorn app.main:app --reload
    ```
    Backend sẽ chạy tại `http://localhost:8000`. Bạn có thể truy cập `http://localhost:8000/docs` để xem tài liệu API.

7.  **(Tùy chọn) Khởi Chạy Frontend (Streamlit):**
    Mở một terminal mới (giữ cho FastAPI đang chạy), kích hoạt lại môi trường ảo nếu cần, sau đó chạy:
    ```bash
    streamlit run app.py
    ```
    Giao diện sẽ mở trong trình duyệt của bạn thường tại `http://localhost:8501`.

8.  **Sử Dụng:**
    *   Trên giao diện Streamlit (nếu chạy) hoặc thông qua công cụ như Postman/curl tới endpoint `/ask`, nhập câu hỏi của bạn.
    *   Hệ thống sẽ xử lý và trả về câu trả lời, phòng ban gợi ý và nguồn thông tin.

---