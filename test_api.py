#!/usr/bin/env python3
"""
Simple test script for the API
"""

import requests
import json

def test_api():
    url = "http://localhost:8001/ask"
    data = {"question": "<PERSON><PERSON><PERSON> thế nào để reset mật khẩu?"}
    
    try:
        print("Sending request to API...")
        response = requests.post(url, json=data, timeout=30)
        print(f"Status code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("Response:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"Error response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")

if __name__ == "__main__":
    test_api()
