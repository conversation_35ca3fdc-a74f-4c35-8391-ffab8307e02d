#!/usr/bin/env python3
"""
Script to add sample data to the ChromaDB database for testing
"""

import os
from dotenv import load_dotenv
from vector_db.chroma_client import add_documents

# Load environment variables
load_dotenv()

# Sample documents for testing
sample_documents = [
    {
        "content": "<PERSON><PERSON> reset mật khẩu, bạn cần truy cập trang đăng nhập và nhấp vào 'Quên mật khẩu'. <PERSON><PERSON> thống sẽ gửi email hướng dẫn đến địa chỉ email đã đăng ký của bạn.",
        "filename": "huong_dan_reset_password.pdf"
    },
    {
        "content": "Khi gặp lỗi kết nối mạng, hãy kiểm tra cáp mạng, khởi động lại router và liên hệ bộ phận kỹ thuật nếu vấn đề vẫn tiếp tục.",
        "filename": "xu_ly_loi_mang.docx"
    },
    {
        "content": "<PERSON><PERSON> cập nhật thông tin cá nhân, đă<PERSON> nhập vào tài khoản và vào mục 'Thông tin cá nhân'. Thay đổi thông tin cần thiết và nhấn 'Lưu'.",
        "filename": "cap_nhat_thong_tin.pdf"
    },
    {
        "content": "Bộ phận hỗ trợ khách hàng làm việc từ 8:00 đến 17:00 các ngày trong tuần. Ngoài giờ hành chính, vui lòng gửi email hoặc để lại tin nhắn.",
        "filename": "gio_lam_viec_ho_tro.xlsx"
    },
    {
        "content": "Để báo cáo sự cố kỹ thuật, vui lòng liên hệ hotline 1900-xxxx hoặc gửi email đến <EMAIL> với mô tả chi tiết về vấn đề.",
        "filename": "bao_cao_su_co.pdf"
    }
]

def main():
    print("Đang thêm dữ liệu mẫu vào ChromaDB...")
    try:
        add_documents(sample_documents)
        print(f"Đã thêm thành công {len(sample_documents)} tài liệu mẫu!")
        print("Danh sách tài liệu đã thêm:")
        for i, doc in enumerate(sample_documents, 1):
            print(f"{i}. {doc['filename']}: {doc['content'][:50]}...")
    except Exception as e:
        print(f"Lỗi khi thêm dữ liệu: {e}")

if __name__ == "__main__":
    main()
