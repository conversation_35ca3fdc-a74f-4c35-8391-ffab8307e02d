from fastapi import FastAPI
from pydantic import BaseModel
from app.chatbot import handle_query

app = FastAPI()

class QueryRequest(BaseModel):
    question: str

class QueryResponse(BaseModel):
    answer: str
    department: str
    sources: list

@app.post("/ask", response_model=QueryResponse)
async def ask_question(request: QueryRequest):
    result = handle_query(request.question)
    return result

# Chạy server: uvicorn app.main:app --reload