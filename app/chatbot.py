# import openai
# from vector_db.chroma_client import query_documents

# openai.api_key = "YOUR_OPENAI_API_KEY"

# def generate_response(prompt, context_docs):
#     context_text = "\n\n".join([doc for doc in context_docs])
#     system_prompt = f"""
#     Bạn là một trợ lý AI cho trung tâm gọi điện. Hãy trả lời câu hỏi của người dùng dựa trên thông tin sau:
#     {context_text}
#     Nếu thông tin không đủ, hãy nói rằng bạn không tìm thấy thông tin và gợi ý liên hệ phòng ban phù hợp.
#     """
#     response = openai.ChatCompletion.create(
#         model="gpt-3.5-turbo",
#         messages=[
#             {"role": "system", "content": system_prompt},
#             {"role": "user", "content": prompt}
#         ]
#     )
#     return response.choices[0].message['content'].strip()

# def handle_query(user_query):
#     # 1. T<PERSON>y xuất tài liệu liên quan
#     results = query_documents(user_query)
#     docs = results['documents'][0] # Danh sách các đoạn văn bản
    
#     # 2. Sinh câu trả lời
#     answer = generate_response(user_query, docs)
    
#     # 3. (Tùy chọn) Xác định phòng ban - có thể hardcode hoặc dùng rule-based
#     department = "Bộ phận Kỹ Thuật" if "lỗi" in user_query.lower() else "Bộ phận Hỗ Trợ"
    
#     return {
#         "answer": answer,
#         "department": department,
#         "sources": results['metadatas'][0]
#     }


# import openai # Bỏ OpenAI
import google.generativeai as genai
import os
from dotenv import load_dotenv
from vector_db.chroma_client import query_documents

# Load environment variables from .env file
load_dotenv()

# --- Cấu hình Google Generative AI ---
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
if not GOOGLE_API_KEY:
    raise ValueError("Bạn cần thiết lập biến môi trường GOOGLE_API_KEY")

genai.configure(api_key=GOOGLE_API_KEY)

# Chọn model ngôn ngữ, ví dụ: gemini-1.5-flash, gemini-1.5-pro
GENERATIVE_MODEL_NAME = "gemini-1.5-flash" # hoặc "gemini-1.5-pro"
model = genai.GenerativeModel(model_name=GENERATIVE_MODEL_NAME)

def generate_response(prompt, context_docs):
    context_text = "\n\n".join([doc for doc in context_docs])

    # Create a more natural prompt for better responses
    full_prompt = f"""
    Bạn là trợ lý AI chuyên nghiệp của trung tâm hỗ trợ khách hàng.

    Thông tin tham khảo:
    {context_text}

    Câu hỏi: {prompt}

    Hãy trả lời một cách tự nhiên, hữu ích và chính xác. Nếu thông tin không đủ để trả lời đầy đủ, hãy đưa ra lời khuyên phù hợp.
    """

    # Gọi API Gemini để sinh câu trả lời
    try:
        response = model.generate_content(full_prompt)
        # Gemini trả về đối tượng GenerateContentResponse
        if response.text:
            answer = response.text.strip()
        else:
            answer = "Xin lỗi, tôi không thể tạo câu trả lời lúc này. Vui lòng thử lại hoặc liên hệ bộ phận hỗ trợ."
    except Exception as e:
        print(f"Lỗi khi gọi API Gemini: {e}")
        answer = "Xin lỗi, hiện tại hệ thống gặp sự cố. Vui lòng thử lại sau hoặc liên hệ bộ phận hỗ trợ để được giúp đỡ."

    return answer

def handle_query(user_query):
    # 1. Truy xuất tài liệu liên quan
    results = query_documents(user_query)
    docs = results['documents'][0] # Danh sách các đoạn văn bản
    
    # 2. Sinh câu trả lời
    answer = generate_response(user_query, docs)
    
    # 3. (Tùy chọn) Xác định phòng ban - có thể hardcode hoặc dùng rule-based
    department = "Bộ phận Kỹ Thuật" if "lỗi" in user_query.lower() else "Bộ phận Hỗ Trợ"
    
    return {
        "answer": answer,
        "department": department,
        "sources": results['metadatas'][0]
    }
