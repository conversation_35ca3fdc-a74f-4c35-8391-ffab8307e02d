#!/usr/bin/env python3
"""
Test the new API key and get clean AI responses
"""

import requests
import json
import time

def test_api():
    url = "http://localhost:8001/ask"
    
    # Test questions
    questions = [
        "<PERSON><PERSON><PERSON> thế nào để reset mật khẩu?",
        "<PERSON>ôi gặp lỗi kết nối mạng",
        "<PERSON><PERSON><PERSON> cập nhật thông tin cá nhân",
        "Giờ làm việc của bộ phận hỗ trợ"
    ]
    
    for i, question in enumerate(questions, 1):
        print(f"\n=== Test {i}: {question} ===")
        
        try:
            response = requests.post(url, json={"question": question}, timeout=30)
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"Answer: {result['answer']}")
                print(f"Department: {result['department']}")
                print(f"Sources: {result['sources']}")
            else:
                print(f"Error: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"Request failed: {e}")
        
        # Wait between requests to avoid rate limiting
        if i < len(questions):
            time.sleep(2)

if __name__ == "__main__":
    print("Testing API with new key...")
    test_api()
