import streamlit as st
import requests

st.title("AI Call Center Assistant")

user_input = st.text_input("Bạn cần hỗ trợ gì?")

if st.button("Gửi"):
    if user_input:
        response = requests.post("http://localhost:8001/ask", json={"question": user_input})
        if response.status_code == 200:
            data = response.json()
            st.write("### Câu trả lời:")
            st.write(data['answer'])
            st.write(f"**Phòng ban nên liên hệ:** {data['department']}")
            st.write("**Nguồn thông tin:**")
            for source in data['sources']:
                st.write(f"- {source['source']}")
        else:
            st.error("Có lỗi xảy ra khi xử lý yêu cầu.")