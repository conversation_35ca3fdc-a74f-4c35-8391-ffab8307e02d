import os
from PyPDF2 import PdfReader
from docx import Document
import pandas as pd

def extract_text_from_pdf(pdf_path):
    reader = PdfReader(pdf_path)
    text = ""
    for page in reader.pages:
        text += page.extract_text()
    return text

def extract_text_from_docx(docx_path):
    doc = Document(docx_path)
    return "\n".join([para.text for para in doc.paragraphs])

def extract_text_from_xlsx(xlsx_path):
    df = pd.read_excel(xlsx_path)
    return df.to_string()

# Hàm tổng hợp
def process_documents(folder_path):
    processed_texts = []
    for filename in os.listdir(folder_path):
        file_path = os.path.join(folder_path, filename)
        try:
            if filename.endswith(".pdf"):
                text = extract_text_from_pdf(file_path)
            elif filename.endswith(".docx"):
                text = extract_text_from_docx(file_path)
            elif filename.endswith(".xlsx"):
                text = extract_text_from_xlsx(file_path)
            else:
                continue
            processed_texts.append({"filename": filename, "content": text})
        except Exception as e:
            print(f"Error processing {filename}: {e}")
    return processed_texts